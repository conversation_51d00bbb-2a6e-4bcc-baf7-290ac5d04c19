<script setup lang="ts">
// 会议现场照片组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// 类型定义
interface ConferencePhotoItem {
  tempId: string;                // 临时ID
  serialNumber: number;          // 序号（仅用于显示）
  subType: string;               // 类型（用户输入）
  paths: string[];               // 附件路径数组
  photos: UploadFile[];          // 附件文件列表
}

// 文件上传相关常量
const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.jpg,.jpeg,.png,.gif';



const props = defineProps({
  conferencePhotoList: {
    type: Array as () => ConferencePhotoItem[],
    default: () => [],
  },
});

const emit = defineEmits(['conferencePhotosEmit']);

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';
  
  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;
  
  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
  
  return `${truncatedName}.${extension}`;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType = SUPPORTED_IMAGE_TYPES.includes(file.type);
  
  if (!isValidType) {
    message.error('只支持上传 JPG、PNG、GIF 格式的图片！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`图片大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许上传
};

// 新增附件分类
const handleAddPhotoCategory = () => {
  const newItem: ConferencePhotoItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: props.conferencePhotoList.length + 1,
    subType: '',
    paths: [],
    photos: [],
  };

  const updatedList = [...props.conferencePhotoList, newItem];
  emit('conferencePhotosEmit', updatedList);
};

// 更新照片字段
const updatePhoto = (itemId: string, field: string, value: any) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedList);
};

// 照片上传处理
const handlePhotoUpload = (options: any, itemId: string) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };

      const updatedList = props.conferencePhotoList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            photos: [...item.photos, fileObj],
            paths: [...item.paths, response.path ? baseUrl + response.path : ''],
          };
        }
        return item;
      });

      emit('conferencePhotosEmit', updatedList);
      message.success(`照片 ${options.file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`照片 ${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除照片
const handleRemovePhoto = (file: UploadFile, itemId: string) => {
  const updatedList = props.conferencePhotoList.map((item) => {
    if (item.tempId === itemId) {
      const photoIndex = item.photos.findIndex(photo => photo.uid === file.uid);
      if (photoIndex > -1) {
        const newPhotos = [...item.photos];
        const newPaths = [...item.paths];
        newPhotos.splice(photoIndex, 1);
        newPaths.splice(photoIndex, 1);
        
        return {
          ...item,
          photos: newPhotos,
          paths: newPaths,
        };
      }
    }
    return item;
  });
  emit('conferencePhotosEmit', updatedList);
  message.success('照片删除成功');
};

// 删除整个分类
const handleRemoveCategory = (itemId: string) => {
  const updatedList = props.conferencePhotoList
    .filter(item => item.tempId !== itemId)
    .map((item, index) => ({ ...item, serialNumber: index + 1 })); // 重新排序
  emit('conferencePhotosEmit', updatedList);
  message.success('分类删除成功');
};

// 文件预览
const handlePreviewPhoto = (file: UploadFile) => {
  previewImage.value = file.url || file.filePath || '';
  previewVisible.value = true;
  previewTitle.value = file.name || '文件预览';
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewImage.value = '';
  previewTitle.value = '';
};

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop() || '';

  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
    return '🖼️';
  }

  return '�';
};

onMounted(() => {
  // 确保初始化时默认有一行空数据
  if (!props.conferencePhotoList || props.conferencePhotoList.length === 0) {
    // 使用 nextTick 确保组件完全初始化后再添加默认条目
    import('vue').then(({ nextTick }) => {
      nextTick(() => {
        handleAddPhotoCategory();
      });
    });
  }
});
</script>

<template>
  <!-- 会议现场照片 -->
  <div class="scheme_conference_photos">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>会议现场照片</span>
      <span class="tip-text">请按类型上传会议现场相关附件</span>
    </div>
    
    <!-- 表格布局 -->
    <div class="info-table-wrapper conference-photos-table">
      <div class="table-header">
        <div class="col-serial">序号</div>
        <div class="col-type">类型</div>
        <div class="col-files">附件</div>
        <div class="col-action">操作</div>
      </div>
      
      <div class="table-body">
        <div v-for="item in conferencePhotoList" :key="item.tempId" class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 类型 -->
          <div class="col-type">
            <a-input
              :value="item.subType"
              placeholder="请输入类型"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(e: Event) => updatePhoto(item.tempId, 'subType', (e.target as HTMLInputElement).value)"
            />
          </div>

          <!-- 附件 -->
          <div class="col-files">
            <div class="files-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.photos.length > 0">
                <a-tag
                  v-for="photo in item.photos"
                  :key="photo.uid"
                  closable
                  class="file-tag"
                  @click="() => handlePreviewPhoto(photo)"
                  @close="() => handleRemovePhoto(photo, item.tempId)"
                >
                  <span class="file-icon">{{ getFileIcon(photo.name) }}</span>
                  {{ getFileDisplayName(photo.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 -->
              <a-upload
                :file-list="[]"
                :before-upload="beforeUpload"
                :custom-request="(options: any) => handlePhotoUpload(options, item.tempId)"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
                multiple
              >
                <a-button size="small" type="link" :loading="uploadLoading">
                  <upload-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 操作 -->
          <div class="col-action">
            <a-button
              type="link"
              danger
              size="small"
              @click="() => handleRemoveCategory(item.tempId)"
            >
              <delete-outlined />
            </a-button>
          </div>
        </div>

        <!-- 添加按钮行 -->
        <div class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddPhotoCategory">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>新增附件分类</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewTitle }}</h4>
        </div>
        <div class="preview-body">
          <img
            v-if="previewImage"
            :src="previewImage"
            alt="照片预览"
            style="width: 100%; max-height: 600px; object-fit: contain"
          />
          <div v-else class="no-image">
            <p>暂无可预览的照片内容</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
.scheme_conference_photos {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 16px;
      background: #1890ff;
      border-radius: 2px;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    margin-bottom: 0;

    .table-header {
      display: flex;
      background-color: #fafafa;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        border-right: 1px solid #d9d9d9;
      }
      .col-type {
        width: 200px;
        border-right: 1px solid #d9d9d9;
      }
      .col-files {
        flex: 1;
        min-width: 300px;
        border-right: 1px solid #d9d9d9;
      }
      .col-action {
        width: 60px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 12px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: center;
              color: #1890ff;
              font-size: 14px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 60px;
          border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }
        .col-type {
          width: 200px;
        }
        .col-files {
          flex: 1;
          min-width: 300px;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .files-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: center;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 10px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                padding: 2px 4px;
                margin: 1px;
                display: flex;
                align-items: center;
                gap: 2px;

                .file-icon {
                  font-size: 12px;
                }

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
        .col-action {
          width: 60px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .no-image {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
