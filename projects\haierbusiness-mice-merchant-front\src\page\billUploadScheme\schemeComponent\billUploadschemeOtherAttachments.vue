<script setup lang="ts">
// 其他附件组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, nextTick } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';

// 类型定义
interface OtherAttachmentItem {
  tempId: string;                // 临时ID
  serialNumber: number;          // 序号
  description: string;           // 其他附件说明
  paths: string[];               // 附件路径数组
  files: UploadFile[];           // 附件文件列表
}

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = [
  'application/pdf', 
  'image/jpeg', 
  'image/jpg', 
  'image/png', 
  'image/gif',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain'
];
const FILE_SIZE_LIMIT = 20; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt';



const props = defineProps({
  otherAttachmentList: {
    type: Array as () => OtherAttachmentItem[],
    default: () => [],
  },
});

const emit = defineEmits(['otherAttachmentsEmit']);

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';
  
  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;
  
  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
  
  return `${truncatedName}.${extension}`;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType = SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx') ||
    file.name.toLowerCase().endsWith('.xls') ||
    file.name.toLowerCase().endsWith('.xlsx') ||
    file.name.toLowerCase().endsWith('.ppt') ||
    file.name.toLowerCase().endsWith('.pptx') ||
    file.name.toLowerCase().endsWith('.txt');

  if (!isValidType) {
    message.error('支持上传 PDF、图片、Office文档、文本文件格式！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许上传
};

// 新增附件分类
const handleAddAttachmentCategory = () => {
  const newItem: OtherAttachmentItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: props.otherAttachmentList.length + 1,
    description: '',
    paths: [],
    files: [],
  };

  const updatedList = [...props.otherAttachmentList, newItem];
  emit('otherAttachmentsEmit', updatedList);
};

// 更新附件字段
const updateAttachment = (itemId: string, field: string, value: any) => {
  const updatedList = props.otherAttachmentList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('otherAttachmentsEmit', updatedList);
};

// 文件上传处理
const handleFileUpload = (options: any, itemId: string) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };

      const updatedList = props.otherAttachmentList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            files: [...item.files, fileObj],
            paths: [...item.paths, response.path ? baseUrl + response.path : ''],
          };
        }
        return item;
      });

      emit('otherAttachmentsEmit', updatedList);
      message.success(`文件 ${options.file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`文件 ${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除文件
const handleRemoveFile = (file: UploadFile, itemId: string) => {
  const updatedList = props.otherAttachmentList.map((item) => {
    if (item.tempId === itemId) {
      const fileIndex = item.files.findIndex(f => f.uid === file.uid);
      if (fileIndex > -1) {
        const newFiles = [...item.files];
        const newPaths = [...item.paths];
        newFiles.splice(fileIndex, 1);
        newPaths.splice(fileIndex, 1);
        
        return {
          ...item,
          files: newFiles,
          paths: newPaths,
        };
      }
    }
    return item;
  });
  emit('otherAttachmentsEmit', updatedList);
  message.success('文件删除成功');
};

// 删除整个分类
const handleRemoveCategory = (itemId: string) => {
  const updatedList = props.otherAttachmentList
    .filter(item => item.tempId !== itemId)
    .map((item, index) => ({ ...item, serialNumber: index + 1 })); // 重新排序
  emit('otherAttachmentsEmit', updatedList);
  message.success('分类删除成功');
};

// 文件预览
const handlePreviewFile = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop() || '';
  
  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
    return '🖼️';
  } else if (['pdf'].includes(ext)) {
    return '📄';
  } else if (['doc', 'docx'].includes(ext)) {
    return '📝';
  } else if (['xls', 'xlsx'].includes(ext)) {
    return '📊';
  } else if (['ppt', 'pptx'].includes(ext)) {
    return '📋';
  } else if (['txt'].includes(ext)) {
    return '📃';
  }
  
  return '📎';
};



onMounted(() => {
  // 确保初始化时默认有一行空数据
  if (!props.otherAttachmentList || props.otherAttachmentList.length === 0) {
    // 使用 nextTick 确保组件完全初始化后再添加默认条目
    import('vue').then(({ nextTick }) => {
      nextTick(() => {
        handleAddAttachmentCategory();
      });
    });
  }
});
</script>

<template>
  <!-- 其他附件 -->
  <div class="scheme_other_attachments">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>其他附件</span>
      <span class="tip-text">请按类型上传其他相关附件文档</span>
    </div>
    
    <!-- 表格布局 -->
    <div class="info-table-wrapper other-attachments-table">
      <div class="table-header">
        <div class="col-serial">序号</div>
        <div class="col-description">其他附件说明</div>
        <div class="col-files">附件文件</div>
        <div class="col-action">操作</div>
      </div>
      
      <div class="table-body">
        <div v-for="item in otherAttachmentList" :key="item.tempId" class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 其他附件说明 -->
          <div class="col-description">
            <a-input
              :value="item.description"
              placeholder="请输入其他附件说明"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(e: Event) => updateAttachment(item.tempId, 'description', (e.target as HTMLInputElement).value)"
            />
          </div>
          
          <!-- 附件文件 -->
          <div class="col-files">
            <div class="files-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.files.length > 0">
                <a-tag
                  v-for="file in item.files"
                  :key="file.uid"
                  closable
                  class="file-tag"
                  @click="() => handlePreviewFile(file)"
                  @close="() => handleRemoveFile(file, item.tempId)"
                >
                  <span class="file-icon">{{ getFileIcon(file.name) }}</span>
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 -->
              <a-upload
                :file-list="[]"
                :before-upload="beforeUpload"
                :custom-request="(options: any) => handleFileUpload(options, item.tempId)"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
                multiple
              >
                <a-button size="small" type="link" :loading="uploadLoading">
                  <upload-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 操作 -->
          <div class="col-action">
            <a-button
              type="link"
              danger
              size="small"
              @click="() => handleRemoveCategory(item.tempId)"
            >
              <delete-outlined />
            </a-button>
          </div>
        </div>

        <!-- 添加按钮行 -->
        <div class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddAttachmentCategory">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>新增附件分类</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <img
              v-if="
                previewFile.name &&
                (previewFile.name.toLowerCase().includes('.jpg') ||
                  previewFile.name.toLowerCase().includes('.jpeg') ||
                  previewFile.name.toLowerCase().includes('.png') ||
                  previewFile.name.toLowerCase().includes('.gif'))
              "
              :src="previewFile.url"
              alt="预览图片"
              style="max-width: 100%; max-height: 500px; object-fit: contain"
            />
            <iframe
              v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url"
              style="width: 100%; height: 500px; border: none"
            ></iframe>
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}
.scheme_other_attachments {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 16px;
      background: #1890ff;
      border-radius: 2px;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    margin-bottom: 0;

    .table-header {
      display: flex;
      background-color: #fafafa;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        border-right: 1px solid #d9d9d9;
      }
      .col-description {
        width: 200px;
        border-right: 1px solid #d9d9d9;
      }
      .col-files {
        flex: 1;
        min-width: 300px;
        border-right: 1px solid #d9d9d9;
      }
      .col-action {
        width: 60px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 12px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: center;
              color: #1890ff;
              font-size: 14px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 60px;
          border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }
        .col-description {
          width: 200px;
        }
        .col-files {
          flex: 1;
          min-width: 300px;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .files-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: center;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 10px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                padding: 2px 4px;
                margin: 1px;
                display: flex;
                align-items: center;
                gap: 2px;

                .file-icon {
                  font-size: 12px;
                }

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
        .col-action {
          width: 60px;
          display: flex;
          justify-content: center;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
