<script setup lang="ts">
// 水单信息组件
import { message } from 'ant-design-vue';
import { onMounted, ref, defineProps, defineEmits, watch } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';

// 类型定义
interface WaterBillItem {
  tempId: string;                // 临时ID
  serialNumber: number;          // 序号（不传递给父组件）
  occurDate: Dayjs | string | null;      // 时间字段 - 内部使用Dayjs，传递给父组件时转为string
  localCurrency: number | null;  // 当地货币
  exchangeRate: number | null;   // 汇率
  totalAmountCny: number;        // 水单总金额（人民币）
  relatedAmountTotalCny: string; // 关联金额合计
  relatedBill: string;
  paths: string[];               // 附件路径数组
  ratePaths: string[];           // 汇率截图路径数组
  exchangeRateScreenshot?: UploadFile;
  unit: string;                  // 单位字段
  attachmentFiles: UploadFile[]; // 用于UI显示的附件文件对象
}

// 文件上传相关常量
const SUPPORTED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const FILE_SIZE_LIMIT = 10; // MB
const UPLOAD_ACCEPT = '.pdf,.jpg,.jpeg,.png,.gif,.doc,.docx';

const props = defineProps({
  waterBillList: {
    type: Array as () => WaterBillItem[],
    default: () => [],
  },
});

const emit = defineEmits(['waterBillEmit', 'viewRelatedBill']);

// 暴露给父组件的方法
defineExpose({
  getWaterBillDataForSubmit: () => getWaterBillDataForSubmit(props.waterBillList)
});

// 响应式数据
const uploadLoading = ref(false);
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};





// 新增水单
const handleAddWaterBill = () => {
  try {
    const newWaterBill: WaterBillItem = {
      tempId: `${Date.now()}_${Math.random()}`,
      serialNumber: props.waterBillList.length + 1, // 序号
      occurDate: null,  // 时间字段，初始为空
      localCurrency: null,
      exchangeRate: null,
      totalAmountCny: 0,  // 水单总金额
      relatedAmountTotalCny: '0元',  // 关联金额合计
      relatedBill: '查看>>',
      paths: [],  // 附件路径数组，初始为空
      ratePaths: [],  // 汇率截图路径数组，初始为空
      unit: '',       // 单位字段，初始为空
      attachmentFiles: [], // 用于UI显示的附件文件对象，初始为空
    };

    const updatedList = [...props.waterBillList, newWaterBill];
    emit('waterBillEmit', getWaterBillDataForParent(updatedList));
    message.success('水单记录创建成功，请上传附件');
  } catch (error) {
    console.error('新增水单出错:', error);
    message.error('新增水单失败，请重试');
  }
};

// 附件上传
const handleAttachmentUpload = (options: any, itemId: string) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 确保路径正确拼接
      const filePath = it.path || '';
      const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

      console.log('文件上传成功，路径:', filePath, '完整URL:', fullUrl);

      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: fullUrl,
        filePath: filePath,
        fileName: options.file.name,
      };

      // 创建一个深拷贝的列表以避免引用问题
      const updatedList = JSON.parse(JSON.stringify(props.waterBillList));

      // 找到并更新对应的水单项
      const waterBillToUpdate = updatedList.find((item: WaterBillItem) => item.tempId === itemId);
      if (waterBillToUpdate) {
        // 确保attachmentFiles数组存在
        waterBillToUpdate.attachmentFiles = waterBillToUpdate.attachmentFiles || [];
        // 添加新文件
        waterBillToUpdate.attachmentFiles.push(fileObj);
        // 更新paths数组
        waterBillToUpdate.paths = waterBillToUpdate.paths || [];
        waterBillToUpdate.paths.push(filePath);
      }

      // 发送更新给父组件
      const dataToSend = getWaterBillDataForParent(updatedList);
      console.log('发送到父组件的数据:', dataToSend);
      emit('waterBillEmit', dataToSend);
      message.success('附件上传成功');
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('附件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 删除附件
const handleRemoveAttachment = (file: UploadFile, itemId: string) => {
  const updatedList = props.waterBillList.map((item) => {
    if (item.tempId === itemId) {
      const newAttachmentFiles = (item.attachmentFiles || []).filter(f => f.uid !== file.uid);

      // 从paths中删除对应的路径
      let newPaths = [...(item.paths || [])];
      if (file.filePath) {
        const pathToRemove = file.filePath.replace(baseUrl, '');
        newPaths = newPaths.filter(path => path !== pathToRemove);
      }

      return {
        ...item,
        attachmentFiles: newAttachmentFiles,
        paths: newPaths
      };
    }
    return item;
  });

  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
  message.success('附件删除成功');
};

// 删除水单
const handleRemoveWaterBill = (itemId: string) => {
  const updatedList = props.waterBillList.filter(item => item.tempId !== itemId);
  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
  message.success('水单删除成功');
};

// 计算水单总金额
const calculateTotalAmountCny = (localCurrency: number | null, exchangeRate: number | null): number => {
  if (localCurrency !== null && localCurrency !== undefined &&
      exchangeRate !== null && exchangeRate !== undefined &&
      localCurrency > 0 && exchangeRate > 0) {
    return localCurrency * exchangeRate;
  }
  return 0;
};

// 更新水单的当地货币
const updateWaterBillLocalCurrency = (itemId: string, value: number | null) => {
  const updatedList = props.waterBillList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        localCurrency: value,
        totalAmountCny: calculateTotalAmountCny(value, item.exchangeRate),
      };
    }
    return item;
  });
  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
};

// 更新水单的汇率
const updateWaterBillExchangeRate = (itemId: string, value: number | null) => {
  const updatedList = props.waterBillList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        exchangeRate: value,
        totalAmountCny: calculateTotalAmountCny(item.localCurrency, value),
      };
    }
    return item;
  });
  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
};

// 更新水单的时间
const updateWaterBillOccurDate = (itemId: string, value: Dayjs | null) => {
  const updatedList = props.waterBillList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        occurDate: value,
      };
    }
    return item;
  });
  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
};

// 更新水单的单位
const updateWaterBillUnit = (itemId: string, value: string) => {
  const updatedList = props.waterBillList.map((item) => {
    if (item.tempId === itemId) {
      return {
        ...item,
        unit: value,
      };
    }
    return item;
  });
  emit('waterBillEmit', getWaterBillDataForParent(updatedList));
};

// 汇率截图上传
const handleExchangeRateScreenshotUpload = (options: any, itemId: string) => {
  // 验证文件类型（只允许图片）
  const file = options.file;
  const isValidType = file.type.startsWith('image/');

  if (!isValidType) {
    message.error('汇率截图只支持上传图片格式的文件！');
    return;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return;
  }

  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 确保路径正确拼接
      const filePath = it.path || '';
      const fullUrl = filePath ? (filePath.startsWith('/') ? baseUrl + filePath : baseUrl + '/' + filePath) : '';

      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: fullUrl,
        filePath: filePath,
        fileName: options.file.name,
      };

      const updatedList = props.waterBillList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            exchangeRateScreenshot: fileObj,
            ratePaths: [filePath]  // 更新汇率截图路径数组
          };
        }
        return item;
      });

      emit('waterBillEmit', getWaterBillDataForParent(updatedList));
      message.success('汇率截图上传成功');
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('汇率截图上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件预览
const handlePreviewFile = (file: UploadFile | null, fileName?: string) => {
  if (file) {
    previewFile.value = file;
    previewFileName.value = file.name;
  } else if (fileName) {
    previewFile.value = null;
    previewFileName.value = fileName;
  }
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 查看关联账单
const handleViewRelatedBill = (item: WaterBillItem) => {
  emit('viewRelatedBill', item);
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 日期格式化
const formatDate = (date: Dayjs | string | null): string | null => {
  if (!date) return null;
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 将字符串日期转换为Dayjs对象
const parseDate = (date: Dayjs | string | null): Dayjs | null => {
  if (!date) return null;
  if (typeof date === 'string') {
    return dayjs(date);
  }
  return date;
};

// 过滤序号字段，保留UI字段以便在父组件和子组件之间共享，拼接完整URL传递给父组件
const getWaterBillDataForParent = (waterBillList: WaterBillItem[]) => {
  return waterBillList.map(item => {
    // 只过滤序号，保留 attachmentFiles 和 exchangeRateScreenshot 以便在父组件和子组件之间共享
    const { serialNumber, ...rest } = item;

    // 将 paths 数组中的路径拼接上 baseUrl
    const fullPaths = (item.paths || []).map(path => {
      if (!path) return '';
      // 如果path已经以/开头，直接拼接；否则添加/
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 将 ratePaths 数组中的路径拼接上 baseUrl，并重命名为 path
    const fullRatePath = (item.ratePaths || []).map(path => {
      if (!path) return '';
      // 如果path已经以/开头，直接拼接；否则添加/
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    return {
      ...rest,
      occurDate: formatDate(item.occurDate),
      paths: fullPaths,
      ratePaths: fullRatePath,
      path: fullRatePath,  // 添加 path 字段（ratePaths 的别名）
      // 保留UI字段以便父子组件数据同步
      attachmentFiles: item.attachmentFiles || [],
      exchangeRateScreenshot: item.exchangeRateScreenshot
    };
  });
};

// 获取用于提交的水单数据（只包含API需要的字段）
const getWaterBillDataForSubmit = (waterBillList: WaterBillItem[]) => {
  return waterBillList.map(item => {
    // 将 paths 数组中的路径拼接上 baseUrl
    const fullPaths = (item.paths || []).map(path => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 将 ratePaths 数组中的路径拼接上 baseUrl
    const fullRatePath = (item.ratePaths || []).map(path => {
      if (!path) return '';
      return path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
    });

    // 只返回API需要的字段
    return {
      tempId: item.tempId,
      occurDate: formatDate(item.occurDate),
      localCurrency: item.localCurrency,
      unit: item.unit,
      exchangeRate: item.exchangeRate,
      totalAmountCny: item.totalAmountCny,
      relatedAmountTotalCny: item.relatedAmountTotalCny,
      paths: fullPaths,
      path: fullRatePath.length > 0 ? fullRatePath[0] : null  // path是单个字符串，不是数组
    };
  });
};

// 初始化水单数据，确保包含必要字段
const initializeWaterBillData = () => {
  if (props.waterBillList && props.waterBillList.length > 0) {
    console.log('初始化水单数据:', props.waterBillList);

    const updatedList = props.waterBillList.map((item, index) => {
      // 确保每个项目都有序号和附件文件数组，并正确处理日期类型
      const processedItem = {
        ...item,
        serialNumber: index + 1, // 总是按照当前索引+1重新分配序号
        attachmentFiles: item.attachmentFiles || [],
        occurDate: parseDate(item.occurDate), // 确保日期是Dayjs对象或null
      };

      // 如果有 paths 但没有 attachmentFiles，需要从 paths 创建 attachmentFiles
      if (item.paths && item.paths.length > 0 && (!item.attachmentFiles || item.attachmentFiles.length === 0)) {
        console.log('从paths创建attachmentFiles:', item.paths);

        processedItem.attachmentFiles = item.paths.map((path, pathIndex) => {
          // 处理路径，确保正确格式
          let processedPath = path;
          // 移除baseUrl前缀(如果有)
          if (path.startsWith('http') && baseUrl) {
            processedPath = path.replace(new RegExp(`^${baseUrl}`), '');
          }

          const fullUrl = processedPath.startsWith('http') ? processedPath :
                         (processedPath.startsWith('/') ? baseUrl + processedPath : baseUrl + '/' + processedPath);

          console.log(`文件${pathIndex + 1}:`, {
            路径: processedPath,
            完整URL: fullUrl
          });

          return {
            uid: `${item.tempId || Date.now()}_${pathIndex}`,
            name: `附件${pathIndex + 1}`,
            status: 'done' as const,
            url: fullUrl,
            filePath: processedPath,
            fileName: `附件${pathIndex + 1}`,
          };
        });
      }

      return processedItem;
    });

    console.log('更新后的水单列表:', updatedList);

    // 将更新后的数据直接应用到当前列表
    updatedList.forEach((item, index) => {
      if (props.waterBillList[index]) {
        // 直接更新UI所需的属性
        props.waterBillList[index].serialNumber = item.serialNumber;
        props.waterBillList[index].attachmentFiles = item.attachmentFiles;
      }
    });

    // 如果数据有变化，发射更新事件
    const hasChanges = updatedList.some((item, index) =>
      !props.waterBillList[index] ||
      item.serialNumber !== props.waterBillList[index]?.serialNumber ||
      item.attachmentFiles?.length !== props.waterBillList[index]?.attachmentFiles?.length
    );

    if (hasChanges) {
      console.log('水单数据有变化，发送更新');
      emit('waterBillEmit', getWaterBillDataForParent(updatedList));
    }
  }
};

// 监听waterBillList的变化，重新计算序号
watch(
  () => props.waterBillList,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 重新分配序号
      const updatedList = newVal.map((item, index) => ({
        ...item,
        serialNumber: index + 1
      }));

      // 只更新本地显示，不触发向父组件的事件
      props.waterBillList.forEach((item, index) => {
        if (updatedList[index]) {
          item.serialNumber = updatedList[index].serialNumber;
        }
      });
    }
  },
  { deep: true }
);

onMounted(() => {
  // 初始化数据
  initializeWaterBillData();
});
</script>

<template>
  <!-- 水单信息 -->
  <div class="water-bill-section">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>水单信息:</span>
      <span class="tip-text">水单、联单明细表等后续提交，考虑尾差</span>
    </div>
    <div class="info-table-wrapper water-bill-table">
      <div class="table-header">
        <div class="col-serial">序号</div>
        <div class="col-attachment">附件</div>
        <div class="col-date">时间</div>
        <div class="col-currency">当地货币</div>
        <div class="col-unit">单位</div>
        <div class="col-rate">汇率</div>
        <div class="col-amount">水单总金额（人民币）</div>
        <div class="col-related-amount">关联金额合计</div>
        <div class="col-related-bill">关联账单</div>
      </div>
      <div class="table-body">
        <div v-for="item in waterBillList" :key="item.tempId" class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>
          <!-- 附件 -->
          <div class="col-attachment">
            <div class="attachment-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.attachmentFiles && item.attachmentFiles.length > 0">
                <a-tag
                  v-for="file in item.attachmentFiles"
                  :key="file.uid"
                  closable
                  class="file-tag"
                  @click="() => handlePreviewFile(file)"
                  @close="() => handleRemoveAttachment(file, item.tempId)"
                >
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
              </div>

              <!-- 上传按钮 -->
              <a-upload
                :file-list="[]"
                :custom-request="(options: any) => handleAttachmentUpload(options, item.tempId)"
                :multiple="true"
                :show-upload-list="false"
                :accept="UPLOAD_ACCEPT"
              >
                <a-button size="small" type="link" :loading="uploadLoading">
                  <upload-outlined />
                  上传
                </a-button>
              </a-upload>
            </div>
          </div>
          <div class="col-date">
            <a-date-picker
              :value="parseDate(item.occurDate)"
              placeholder="请输入"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(date: Dayjs | null) => updateWaterBillOccurDate(item.tempId, date)"
            />
          </div>
          <div class="col-currency">
            <a-input-number
              v-model:value="item.localCurrency"
              :min="0"
              :precision="2"
              placeholder="请输入"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(value: number | null) => updateWaterBillLocalCurrency(item.tempId, value)"
            />
          </div>
          <div class="col-unit">
            <a-input
              v-model:value="item.unit"
              placeholder="请输入"
              size="small"
              class="borderless-input"
              :bordered="false"
              @change="(e: Event) => updateWaterBillUnit(item.tempId, (e.target as HTMLInputElement).value)"
            />
          </div>
          <div class="col-rate">
            <div class="rate-content">
              <a-input-number
                v-model:value="item.exchangeRate"
                :min="0"
                :precision="4"
                placeholder="请输入"
                size="small"
                class="borderless-input"
                :bordered="false"
                @change="(value: number | null) => updateWaterBillExchangeRate(item.tempId, value)"
              />
              <div class="screenshot-section">
                <a-tag
                  v-if="item.exchangeRateScreenshot"
                  class="file-tag screenshot-tag"
                  @click="() => handlePreviewFile(item.exchangeRateScreenshot!, '汇率截图')"
                >
                  汇率截图
                </a-tag>
                <a-upload
                  :file-list="[]"
                  :custom-request="(options: any) => handleExchangeRateScreenshotUpload(options, item.tempId)"
                  :show-upload-list="false"
                  accept="image/*"
                >
                  <a-button size="small" type="link" class="screenshot-btn">
                    {{ item.exchangeRateScreenshot ? '重新上传' : '上传截图' }}
                  </a-button>
                </a-upload>
              </div>
            </div>
          </div>
          <div class="col-amount">{{ item.totalAmountCny > 0 ? item.totalAmountCny.toFixed(2) + '元' : '0元' }}</div>
          <div class="col-related-amount">{{ item.relatedAmountTotalCny }}</div>
          <div class="col-related-bill">
            <a-button type="link" size="small" @click="() => handleViewRelatedBill(item)">
              {{ item.relatedBill }}
            </a-button>
          </div>
        </div>
        <!-- 添加按钮行，当没有水单时显示 -->
        <div class="table-row add-row" v-if="waterBillList.length === 0">
          <div class="add-button-full-width" @click="handleAddWaterBill">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>添加水单</span>
            </div>
          </div>
        </div>
        
        <!-- 新增：始终显示的添加水单按钮行 -->
        <div class="table-row add-row" v-if="waterBillList.length > 0">
          <div class="add-button-full-width" @click="handleAddWaterBill">
            <div class="demand_add">
              <div class="demand_add_img mr8"></div>
              <span>添加水单</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal
      v-model:open="previewVisible"
      title="文件预览"
      :footer="null"
      width="80%"
      @cancel="handlePreviewCancel"
    >
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <img
              v-if="previewFile.name && (previewFile.name.toLowerCase().includes('.jpg') || previewFile.name.toLowerCase().includes('.jpeg') || previewFile.name.toLowerCase().includes('.png') || previewFile.name.toLowerCase().includes('.gif'))"
              :src="previewFile.url"
              alt="预览图片"
              style="max-width: 100%; max-height: 500px; object-fit: contain;"
            />
            <iframe
              v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url"
              style="width: 100%; height: 500px; border: none;"
            ></iframe>
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile">
                下载文件
              </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.water-bill-section {
  padding-top: 24px;
  position: relative;
  margin-bottom: 24px;
  .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;;
      border-radius: 2px;
    }

  .contract_title {
    margin-bottom: 20px;
    font-size: 14px;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    span {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
    }
    .tip-text {
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 70%;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    margin-bottom: 0;

    &.water-bill-table {
      width: 100%;
    }

    .table-header {
      display: flex;
      background-color: #fafafa;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      > div {
        padding: 12px 8px;
        text-align: center;
        border-right: 1px solid #d9d9d9;
        
        &:last-child {
          border-right: none;
        }
      }

      .col-serial {
        width: 80px;
      }
      .col-attachment {
        width: 200px;
      }
      .col-date {
        width: 180px;
      }
      .col-currency {
        width: 130px;
      }
      .col-unit {
        width: 100px;
      }
      .col-rate {
        width: 150px;
      }
      .col-amount {
        width: 160px;
      }
      .col-related-amount {
        width: 120px;
      }
      .col-related-bill {
        width: 100px;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }
        
        &.add-row {
          border-bottom: none;

          .add-button-full-width {
            width: 100%;
            padding: 12px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            cursor: pointer;
            border-bottom: none;

            &:hover {
              background-color: #f5f5f5;
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: center;
              color: #1890ff;
              font-size: 14px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
              }
            }
          }
        }

        > div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 60px;
          border-right: 1px solid #f0f0f0;
          
          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }
        .col-attachment {
          width: 200px;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .attachment-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: center;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 10px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                padding: 2px 4px;
                margin: 1px;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
        .col-date {
          width: 180px;
        }
        .col-currency {
          width: 130px;
        }
        .col-unit {
          width: 100px;
        }
        .col-rate {
          width: 150px;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .rate-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;

            .screenshot-section {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;

              .screenshot-tag {
                font-size: 10px;
                padding: 2px 6px;
                cursor: pointer;
              }

              .screenshot-btn {
                font-size: 10px;
                padding: 0;
                height: auto;
                line-height: 1;
              }
            }
          }
        }
        .col-amount {
          width: 160px;
        }
        .col-related-amount {
          width: 120px;
        }
        .col-related-bill {
          width: 100px;
        }

        .file-tag {
          cursor: pointer;
          font-size: 12px;
          background-color: #e6f7ff;
          border-color: #1890ff;
          color: #1890ff;
          padding: 4px 8px;

          &:hover {
            opacity: 0.8;
            background-color: #bae7ff;
          }
        }


      }
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input > input {
      border: none !important;
      box-shadow: none !important;
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}
</style>
